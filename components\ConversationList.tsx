import React from 'react';
import { Conversation } from '../types';
import { formatDistanceToNow } from 'date-fns';

interface ConversationListProps {
  conversations: Conversation[];
  selectedConversation: Conversation | null;
  onSelectConversation: (conversation: Conversation) => void;
  currentUserId: string;
}

const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  selectedConversation,
  onSelectConversation,
  currentUserId
}) => {
  const getOtherParticipant = (conversation: Conversation) => {
    return conversation.participantDetails.find(p => p.id !== currentUserId);
  };

  const formatLastActivity = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return 'Unknown';
    }
  };

  const truncateMessage = (message: string, maxLength: number = 50) => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  if (conversations.length === 0) {
    return (
      <div className="p-4 text-center">
        <div className="text-4xl mb-2 text-neutral-muted">💬</div>
        <p className="text-neutral-muted text-sm">
          No conversations yet. Start a new chat!
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-y-auto h-full">
      {conversations.map((conversation) => {
        const otherParticipant = getOtherParticipant(conversation);
        const unreadCount = conversation.unreadCount[currentUserId] || 0;
        const isSelected = selectedConversation?.id === conversation.id;

        if (!otherParticipant) return null;

        return (
          <div
            key={conversation.id}
            onClick={() => onSelectConversation(conversation)}
            className={`p-4 border-b border-neutral-border cursor-pointer transition-all duration-200 hover:bg-neutral-base/50 ${
              isSelected 
                ? 'bg-neutral-base border-l-4 border-l-brand-primary animate-pulse-glow' 
                : 'hover-scale'
            }`}
          >
            <div className="flex items-center space-x-3">
              {/* Avatar */}
              <div className="relative">
                <img
                  src={otherParticipant.avatarUrl}
                  alt={otherParticipant.username}
                  className="w-12 h-12 rounded-full border-2 border-brand-primary/50 hover-scale transition-transform duration-200"
                />
                {otherParticipant.isActive && (
                  <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-neutral-surface animate-pulse"></div>
                )}
              </div>

              {/* Conversation Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className={`font-semibold truncate ${
                    unreadCount > 0 ? 'text-neutral-100' : 'text-neutral-200'
                  }`}>
                    {otherParticipant.username}
                  </h3>
                  <div className="flex items-center space-x-2">
                    {unreadCount > 0 && (
                      <span className="bg-brand-primary text-white text-xs rounded-full px-2 py-1 animate-pulse-glow">
                        {unreadCount > 99 ? '99+' : unreadCount}
                      </span>
                    )}
                    <span className="text-xs text-neutral-muted">
                      {formatLastActivity(conversation.lastActivity)}
                    </span>
                  </div>
                </div>

                {/* Last Message Preview */}
                {conversation.lastMessage && (
                  <div className="mt-1">
                    <p className={`text-sm truncate ${
                      unreadCount > 0 ? 'text-neutral-200 font-medium' : 'text-neutral-muted'
                    }`}>
                      {conversation.lastMessage.senderId === currentUserId && (
                        <span className="text-brand-primary">You: </span>
                      )}
                      {truncateMessage(conversation.lastMessage.content)}
                    </p>
                  </div>
                )}

                {/* No messages yet */}
                {!conversation.lastMessage && (
                  <p className="text-sm text-neutral-muted mt-1">
                    No messages yet
                  </p>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ConversationList;
