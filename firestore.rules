rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      // Users can read their own profile and any active user's profile
      // <PERSON><PERSON> can read all user profiles
      allow read: if request.auth != null &&
        (request.auth.uid == userId ||
         resource.data.isActive == true ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);

      // Users can only update their own profile (except admin fields)
      // Admins can update any user's profile including admin fields
      // Users can update followers/following fields for follow functionality
      allow update: if request.auth != null &&
        (
          // Regular users updating their own profile (non-admin fields only)
          (request.auth.uid == userId &&
           !('isAdmin' in request.resource.data.diff(resource.data).affectedKeys()) &&
           !('isActive' in request.resource.data.diff(resource.data).affectedKeys()) &&
           !('isPendingApproval' in request.resource.data.diff(resource.data).affectedKeys())) ||
          // Users can update followers/following fields for follow functionality
          (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['followers']) &&
           // Only allow adding/removing the authenticated user to/from followers list
           (request.auth.uid in request.resource.data.get('followers', [])) != (request.auth.uid in resource.data.get('followers', []))) ||
          (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['following']) &&
           // Users can only update their own following list
           request.auth.uid == userId) ||
          // Admins can update any user's profile including admin fields
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true
        );

      // Only admins can create users or modify admin fields
      allow create: if request.auth != null &&
        (request.auth.uid == userId ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);

      // Only admins can delete users
      allow delete: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Posts collection
    match /posts/{postId} {
      // Anyone authenticated can read posts (even if pending approval)
      allow read: if request.auth != null;

      // Users can create posts if they are active (including anonymous posts)
      allow create: if request.auth != null &&
        (request.auth.uid == request.resource.data.userId ||
         (request.resource.data.isAnonymous == true &&
          request.auth.uid == request.resource.data.actualUserId)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;

      // Users can update their own posts if they are active (including anonymous posts they created)
      // OR any active user can update comments/likes fields for liking posts and comments
      allow update: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true &&
        (
          // Post owner can update any field
          (request.auth.uid == resource.data.userId ||
           (resource.data.isAnonymous == true &&
            request.auth.uid == resource.data.actualUserId)) ||
          // Any active user can update likes, likedUsers, or comments fields (for liking posts/comments)
          (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['likes', 'likedUsers', 'comments']))
        );

      // Users can delete their own posts if active, admins can delete any post
      allow delete: if request.auth != null &&
        ((request.auth.uid == resource.data.userId ||
         (resource.data.isAnonymous == true &&
          request.auth.uid == resource.data.actualUserId)) &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true) ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Comments subcollection
    match /posts/{postId}/comments/{commentId} {
      // Anyone authenticated can read comments (even if pending approval)
      allow read: if request.auth != null;

      // Users can create comments if they are active
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.userId &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;

      // Users can update their own comments if they are active
      allow update: if request.auth != null &&
        request.auth.uid == resource.data.userId &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;

      // Users can delete their own comments if active, admins can delete any comment
      allow delete: if request.auth != null &&
        (request.auth.uid == resource.data.userId &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true) ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Admin messages collection
    match /adminMessages/{messageId} {
      // Only admins can read admin messages
      allow read: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;

      // Anyone can create admin messages (contact admin)
      allow create: if request.auth != null;

      // Only admins can update/delete admin messages
      allow update, delete: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Login screen config (admin only)
    match /config/loginScreen {
      allow read: if true; // Anyone can read login screen config
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Conversations collection
    match /conversations/{conversationId} {
      // Users can read/write conversations they participate in
      allow read, write: if request.auth != null &&
        request.auth.uid in resource.data.participants;
      // Allow creation if user is in participants list
      allow create: if request.auth != null &&
        request.auth.uid in request.resource.data.participants;
    }

    // Messages collection
    match /messages/{messageId} {
      // Users can read messages from conversations they participate in
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId)) &&
        request.auth.uid in get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.participants;

      // Users can create messages in conversations they participate in
      allow create: if request.auth != null &&
        exists(/databases/$(database)/documents/conversations/$(request.resource.data.conversationId)) &&
        request.auth.uid in get(/databases/$(database)/documents/conversations/$(request.resource.data.conversationId)).data.participants &&
        request.auth.uid == request.resource.data.senderId;

      // Users can update their own messages (for read status, etc.)
      allow update: if request.auth != null &&
        exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId)) &&
        request.auth.uid in get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.participants;
    }
  }
}
