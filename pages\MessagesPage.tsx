import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { Conversation, Message } from '../types';
import {
  subscribeToUserConversations,
  subscribeToConversationMessages,
  markMessagesAsRead,
  findOrCreateConversation
} from '../services/firebaseService';
import ConversationList from '../components/ConversationList';
import MessageThread from '../components/MessageThread';
import UserSearch from '../components/UserSearch';

const MessagesPage: React.FC = () => {
  const { currentUser } = useAuth();
  const location = useLocation();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [loading, setLoading] = useState(true);

  // Subscribe to conversations
  useEffect(() => {
    if (!currentUser) return;

    const unsubscribe = subscribeToUserConversations(currentUser.id, (updatedConversations) => {
      setConversations(updatedConversations);
      setLoading(false);

      // Auto-select conversation if navigated from MessageButton
      const state = location.state as { conversationId?: string };
      if (state?.conversationId && !selectedConversation) {
        const targetConversation = updatedConversations.find(c => c.id === state.conversationId);
        if (targetConversation) {
          setSelectedConversation(targetConversation);
        }
      }
    });

    return () => unsubscribe();
  }, [currentUser, location.state, selectedConversation]);

  // Subscribe to messages for selected conversation
  useEffect(() => {
    if (!selectedConversation || !currentUser) {
      setMessages([]);
      return;
    }

    const unsubscribe = subscribeToConversationMessages(
      selectedConversation.id,
      currentUser.id,
      (updatedMessages) => {
        setMessages(updatedMessages);

        // Mark messages as read when viewing conversation
        if (updatedMessages.length > 0) {
          markMessagesAsRead(selectedConversation.id, currentUser.id);
        }
      }
    );

    return () => unsubscribe();
  }, [selectedConversation, currentUser]);

  const handleSelectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
  };

  const handleStartNewConversation = async (userId: string) => {
    if (!currentUser) return;

    try {
      const conversationId = await findOrCreateConversation(currentUser.id, userId);

      // Find the conversation in our list or wait for it to appear
      const existingConversation = conversations.find(c => c.id === conversationId);
      if (existingConversation) {
        setSelectedConversation(existingConversation);
      }

      setShowUserSearch(false);
    } catch (error) {
      console.error('Error starting new conversation:', error);
    }
  };

  const getTotalUnreadCount = () => {
    if (!currentUser) return 0;
    return conversations.reduce((total, conversation) => {
      return total + (conversation.unreadCount[currentUser.id] || 0);
    }, 0);
  };

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-neutral-muted">Please log in to access messages.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-8rem)] bg-neutral-base rounded-lg border border-neutral-border cyber-border overflow-hidden">
      <div className="flex h-full">
        {/* Conversations Sidebar */}
        <div className="w-1/3 border-r border-neutral-border bg-neutral-surface">
          <div className="p-4 border-b border-neutral-border">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-neutral-100 animate-text-glow">
                Messages
                {getTotalUnreadCount() > 0 && (
                  <span className="ml-2 bg-brand-primary text-white text-xs rounded-full px-2 py-1 animate-pulse-glow">
                    {getTotalUnreadCount()}
                  </span>
                )}
              </h2>
              <button
                onClick={() => setShowUserSearch(true)}
                className="bg-brand-primary hover:bg-brand-secondary text-white px-3 py-1 rounded-md text-sm transition-all duration-200 hover-scale hover-glow"
              >
                New Chat
              </button>
            </div>
          </div>

          <ConversationList
            conversations={conversations}
            selectedConversation={selectedConversation}
            onSelectConversation={handleSelectConversation}
            currentUserId={currentUser.id}
          />
        </div>

        {/* Message Thread */}
        <div className="flex-1 flex flex-col">
          {selectedConversation ? (
            <MessageThread
              conversation={selectedConversation}
              messages={messages}
              currentUserId={currentUser.id}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center bg-neutral-base">
              <div className="text-center">
                <div className="text-6xl mb-4 text-neutral-muted">💬</div>
                <h3 className="text-xl font-semibold text-neutral-100 mb-2">
                  Select a conversation
                </h3>
                <p className="text-neutral-muted mb-4">
                  Choose a conversation from the sidebar or start a new one
                </p>
                <button
                  onClick={() => setShowUserSearch(true)}
                  className="bg-brand-primary hover:bg-brand-secondary text-white px-4 py-2 rounded-md transition-all duration-200 hover-scale hover-glow"
                >
                  Start New Conversation
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* User Search Modal */}
      {showUserSearch && (
        <UserSearch
          onSelectUser={handleStartNewConversation}
          onClose={() => setShowUserSearch(false)}
          currentUserId={currentUser.id}
        />
      )}
    </div>
  );
};

export default MessagesPage;
